<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Participant Presence Test UI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .participants {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>Participant Presence Service Test UI</h1>

    <!-- JWT Token Input -->
    <div class="container">
        <h2>JWT Token Configuration</h2>
        <div class="form-group">
            <label for="jwtToken">JWT Token:</label>
            <textarea id="jwtToken" rows="3" placeholder="Paste your JWT token here...">eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LXVzZXItMTIzIiwicm9sZSI6Im9wZXJhdG9yIiwiZXhwIjo5OTk5OTk5OTk5fQ.example</textarea>
        </div>
        <button onclick="decodeToken()">Decode Token</button>
        <div id="tokenInfo" class="participants" style="display: none;">
            <h4>Token Information:</h4>
            <div id="tokenDetails"></div>
        </div>
    </div>

    <!-- WebSocket Connection -->
    <div class="container">
        <h2>WebSocket Connection</h2>
        <div id="connectionStatus" class="status disconnected">Disconnected</div>
        <button id="connectBtn" onclick="connectWebSocket()">Connect</button>
        <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>Disconnect</button>

        <div class="form-group" style="margin-top: 15px;">
            <label for="statusSelect">Send Status Update:</label>
            <select id="statusSelect">
                <option value="AVAILABLE">AVAILABLE</option>
                <option value="BUSY">BUSY</option>
            </select>
            <button id="sendStatusBtn" onclick="sendStatus()" disabled>Send Status</button>
        </div>
    </div>

    <!-- GET Endpoints Testing -->
    <div class="container">
        <h2>Available Participants</h2>
        <button onclick="fetchOperators()">Get Available Operators</button>
        <button onclick="fetchTranslators()">Get Available Translators</button>
        <button onclick="fetchBoth()">Get Both</button>

        <div id="participantsData" class="participants">
            <h4>Results:</h4>
            <div id="operatorsResult">Operators: Not fetched yet</div>
            <div id="translatorsResult">Translators: Not fetched yet</div>
        </div>
    </div>

    <!-- Activity Log -->
    <div class="container">
        <h2>Activity Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        const API_BASE = 'http://localhost:8000';

        // Utility functions
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // JWT Token functions
        function decodeToken() {
            const token = document.getElementById('jwtToken').value.trim();
            if (!token) {
                log('ERROR: No token provided');
                return;
            }

            try {
                // Simple JWT decode (header.payload.signature)
                const parts = token.split('.');
                if (parts.length !== 3) {
                    throw new Error('Invalid JWT format');
                }

                const payload = JSON.parse(atob(parts[1]));
                const header = JSON.parse(atob(parts[0]));

                document.getElementById('tokenDetails').innerHTML = `
                    <strong>Header:</strong> ${JSON.stringify(header, null, 2)}<br>
                    <strong>Payload:</strong> ${JSON.stringify(payload, null, 2)}<br>
                    <strong>User ID:</strong> ${payload.sub || 'N/A'}<br>
                    <strong>Role:</strong> ${payload.role || 'N/A'}<br>
                    <strong>Expires:</strong> ${payload.exp ? new Date(payload.exp * 1000).toLocaleString() : 'N/A'}
                `;
                document.getElementById('tokenInfo').style.display = 'block';
                log('Token decoded successfully');
            } catch (error) {
                log(`ERROR: Failed to decode token - ${error.message}`);
                document.getElementById('tokenInfo').style.display = 'none';
            }
        }

        // WebSocket functions
        function connectWebSocket() {
            const token = document.getElementById('jwtToken').value.trim();
            if (!token) {
                log('ERROR: Please enter a JWT token first');
                return;
            }

            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected');
                return;
            }

            const wsUrl = `ws://localhost:8000/ws?token=${encodeURIComponent(token)}`;
            log(`Connecting to: ${wsUrl}`);

            ws = new WebSocket(wsUrl);

            ws.onopen = () => {
                log('WebSocket connected successfully');
                updateConnectionStatus(true);
            };

            ws.onmessage = (event) => {
                log(`Received: ${event.data}`);
            };

            ws.onclose = (event) => {
                log(`WebSocket closed (code: ${event.code}, reason: ${event.reason})`);
                updateConnectionStatus(false);
            };

            ws.onerror = (error) => {
                log(`WebSocket error: ${error}`);
                updateConnectionStatus(false);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                log('Disconnecting WebSocket...');
            }
        }

        function updateConnectionStatus(connected) {
            const statusDiv = document.getElementById('connectionStatus');
            const connectBtn = document.getElementById('connectBtn');
            const disconnectBtn = document.getElementById('disconnectBtn');
            const sendStatusBtn = document.getElementById('sendStatusBtn');

            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                sendStatusBtn.disabled = false;
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                sendStatusBtn.disabled = true;
            }
        }

        function sendStatus() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('ERROR: WebSocket not connected');
                return;
            }

            const status = document.getElementById('statusSelect').value;
            const message = JSON.stringify({ status: status });

            ws.send(message);
            log(`Sent: ${message}`);
        }

        // GET endpoint functions
        async function fetchOperators() {
            try {
                log('Fetching available operators...');
                const response = await fetch(`${API_BASE}/internal/operators/available`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const operators = await response.json();
                document.getElementById('operatorsResult').innerHTML =
                    `Operators: [${operators.join(', ') || 'None'}] (${operators.length} available)`;
                log(`Operators fetched: ${JSON.stringify(operators)}`);
            } catch (error) {
                log(`ERROR fetching operators: ${error.message}`);
                document.getElementById('operatorsResult').innerHTML =
                    `Operators: Error - ${error.message}`;
            }
        }

        async function fetchTranslators() {
            try {
                log('Fetching available translators...');
                const response = await fetch(`${API_BASE}/internal/translators/available`);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const translators = await response.json();
                document.getElementById('translatorsResult').innerHTML =
                    `Translators: [${translators.join(', ') || 'None'}] (${translators.length} available)`;
                log(`Translators fetched: ${JSON.stringify(translators)}`);
            } catch (error) {
                log(`ERROR fetching translators: ${error.message}`);
                document.getElementById('translatorsResult').innerHTML =
                    `Translators: Error - ${error.message}`;
            }
        }

        async function fetchBoth() {
            await Promise.all([fetchOperators(), fetchTranslators()]);
        }

        // Auto-refresh participants every 5 seconds when connected
        setInterval(() => {
            if (ws && ws.readyState === WebSocket.OPEN) {
                fetchBoth();
            }
        }, 5000);

        // Initialize
        log('Test UI loaded. Enter a JWT token and click Connect to start testing.');
    </script>
</body>
</html>