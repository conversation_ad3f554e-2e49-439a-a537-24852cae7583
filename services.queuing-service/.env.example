# Service Configuration
SERVICE_NAME=queuing-service
HOST=0.0.0.0
PORT=8003
DEBUG=false

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=queuing-service

# External Service URLs
PARTICIPANT_PRESENCE_URL=http://localhost:8001

# Queue Configuration
MAX_QUEUE_SIZE=1000
QUEUE_TIMEOUT_SECONDS=300
ASSIGNMENT_TIMEOUT_SECONDS=30

# Routing Strategy Configuration
DEFAULT_ROUTING_STRATEGY=fifo
ENABLE_PRIORITY_ROUTING=true
ENABLE_SKILLS_BASED_ROUTING=true

# Health Check Configuration
HEALTH_CHECK_INTERVAL_SECONDS=30
