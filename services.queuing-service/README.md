# Queuing Service

The Queuing Service is a dedicated microservice responsible for managing all call queues in the Cortexa platform. It implements Automatic Call Distribution (ACD) with various routing strategies including First-In-First-Out (FIFO), priority-based routing, and skills-based routing.

## Features

### Core Functionality
- **Queue Management**: Maintains state of all calls waiting for operator assignment
- **Multiple Routing Strategies**:
  - FIFO (First-In-First-Out)
  - Priority-based routing (critical, high, normal, low)
  - Skills-based routing (language pairs, specific skills)
- **Event-Driven Architecture**: Consumes `call.started` events and publishes `call.queued` and `call.assigned` events
- **External Service Integration**: Communicates with Participant Presence Service for operator availability

### Routing Strategies

#### FIFO (First-In-First-Out)
- Assigns calls to available participants in the order they were received
- Default strategy for standard calls

#### Priority-Based Routing
- Prioritizes calls marked as critical or high priority
- Ensures urgent calls are handled first

#### Skills-Based Routing
- Matches calls requiring specific skills or languages to qualified participants
- Calculates skill scores for optimal assignment
- Falls back to FIFO if no skill matches are found

## API Endpoints

### Public Endpoints

#### `GET /queue/status`
Returns comprehensive queue status including:
- Total queued calls
- Breakdown by priority and role
- Average and longest wait times
- Available participant counts

#### `GET /queue/position/{call_id}`
Returns the current position of a specific call in the queue.

#### `GET /queue/health`
Health check endpoint that verifies:
- Service status
- Participant Presence Service connectivity
- Queue manager status

## Event Integration

### Consumed Events
- **`call.started`**: Initiates the queuing process for new calls

### Published Events
- **`call.queued`**: Published when a call is successfully added to the queue
- **`call.assigned`**: Published when a call is assigned to a participant

## Configuration

The service uses environment variables for configuration. Copy `.env.example` to `.env` and adjust values as needed:

```bash
# Service Configuration
SERVICE_NAME=queuing-service
HOST=0.0.0.0
PORT=8003
DEBUG=false

# Kafka Configuration
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_GROUP_ID=queuing-service

# External Service URLs
PARTICIPANT_PRESENCE_URL=http://localhost:8001

# Queue Configuration
MAX_QUEUE_SIZE=1000
QUEUE_TIMEOUT_SECONDS=300
ASSIGNMENT_TIMEOUT_SECONDS=30

# Routing Strategy Configuration
DEFAULT_ROUTING_STRATEGY=fifo
ENABLE_PRIORITY_ROUTING=true
ENABLE_SKILLS_BASED_ROUTING=true
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. Run the service:
```bash
python main.py
```

Or using uvicorn directly:
```bash
uvicorn main:app --host 0.0.0.0 --port 8003 --reload
```

## Architecture

### Components

#### ApplicationContext
- Singleton pattern for managing shared resources
- Initializes Kafka producers/consumers, external clients, and core services
- Implements fail-early principle with connection testing

#### QueueManager
- Manages call queue state and operations
- Provides priority and role-based queues for efficient retrieval
- Handles automatic cleanup of expired calls
- Thread-safe operations with asyncio locks

#### RoutingEngine
- Implements various routing strategies
- Integrates with Participant Presence Service for availability
- Calculates skill scores for optimal assignments

#### EventHandlers
- Processes incoming Kafka events
- Manages complete workflow from queuing to assignment
- Handles background assignment processing

#### ParticipantPresenceClient
- HTTP client for Participant Presence Service
- Fetches available operators and translators
- Supports health checking and connection management

## Dependencies

- **FastAPI**: Web framework for API endpoints
- **Kafka**: Event streaming for `call.started` consumption and event publishing
- **Participant Presence Service**: External service for operator availability
- **cortexa-common**: Shared utilities for Kafka integration

## Monitoring

The service provides comprehensive health checking and status endpoints for monitoring:

- Queue statistics and wait times
- Participant availability counts
- Dependency health status
- Service performance metrics

## Development

### Project Structure
```
services.queuing-service/
├── api/                    # API route definitions
│   ├── __init__.py
│   └── queue_routes.py
├── services/               # Business logic services
│   ├── __init__.py
│   ├── event_handlers.py
│   ├── participant_presence_client.py
│   ├── queue_manager.py
│   └── routing_engine.py
├── config.py              # Configuration settings
├── context.py             # Application context (singleton)
├── dependencies.py        # FastAPI dependencies
├── models.py              # Data models and schemas
├── main.py                # Application entry point
├── requirements.txt       # Python dependencies
├── .env.example          # Environment configuration template
└── README.md             # This file
```

### Testing

The service is designed with dependency injection to facilitate testing. Key components can be mocked for unit testing:

- Mock ParticipantPresenceClient for external service calls
- Mock Kafka producers/consumers for event testing
- Use ApplicationContext.reset_instance() for test isolation
