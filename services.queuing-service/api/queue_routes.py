"""
API routes for queue management.

This module defines the public API endpoints for viewing queue status
and managing queue operations.
"""

from fastapi import <PERSON>Rout<PERSON>, Depends, HTTPException
from typing import Optional
from uuid import UUID

from context import ApplicationContext, get_app_context
from models import QueueStatusResponse, ParticipantRole

router = APIRouter(
    prefix="/queue",
    tags=["queue"],
    responses={404: {"description": "Not found"}},
)


async def get_queue_context(
    context: ApplicationContext = Depends(get_app_context)
) -> ApplicationContext:
    """
    Dependency to get the application context for queue operations.
    
    Args:
        context: The application context (injected by FastAPI)
        
    Returns:
        ApplicationContext: The application context instance
    """
    return context


@router.get(
    "/status",
    response_model=QueueStatusResponse,
    summary="Get queue status",
    description="Get current queue status including statistics and available participants"
)
async def get_queue_status(
    context: ApplicationContext = Depends(get_queue_context)
) -> QueueStatusResponse:
    """
    Get current queue status and statistics.
    
    This endpoint provides comprehensive information about the current
    state of the call queue, including:
    - Total number of queued calls
    - Breakdown by priority and role
    - Average and longest wait times
    - Available participant counts
    
    Returns:
        QueueStatusResponse: Current queue status and statistics
    """
    try:
        # Get available participant counts
        participant_counts = await context.routing_engine.get_available_participants_count()
        
        # Get queue status with participant counts
        status = await context.queue_manager.get_queue_status(
            available_operators=participant_counts.get(ParticipantRole.OPERATOR, 0),
            available_translators=participant_counts.get(ParticipantRole.TRANSLATOR, 0)
        )
        
        return status
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get queue status: {str(e)}"
        )


@router.get(
    "/position/{call_id}",
    summary="Get call position in queue",
    description="Get the current position of a specific call in the queue"
)
async def get_call_position(
    call_id: UUID,
    context: ApplicationContext = Depends(get_queue_context)
) -> dict:
    """
    Get the position of a specific call in the queue.
    
    Args:
        call_id: The UUID of the call to check
        context: Application context (injected by FastAPI)
        
    Returns:
        Dictionary containing the call's position in the queue
        
    Raises:
        HTTPException: If the call is not found in the queue
    """
    try:
        position = await context.queue_manager.get_queue_position(call_id)
        
        if position is None:
            raise HTTPException(
                status_code=404,
                detail=f"Call {call_id} not found in queue"
            )
        
        return {
            "call_id": str(call_id),
            "position": position,
            "message": f"Call is at position {position} in the queue"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get call position: {str(e)}"
        )


@router.get(
    "/health",
    summary="Health check",
    description="Check the health of the queuing service and its dependencies"
)
async def health_check(
    context: ApplicationContext = Depends(get_queue_context)
) -> dict:
    """
    Perform a health check on the queuing service.
    
    This endpoint checks the health of the service and its dependencies,
    including the participant presence service connection.
    
    Args:
        context: Application context (injected by FastAPI)
        
    Returns:
        Dictionary containing health status information
    """
    try:
        health_status = {
            "service": "queuing-service",
            "status": "healthy",
            "timestamp": __import__('datetime').datetime.utcnow().isoformat(),
            "dependencies": {}
        }
        
        # Check participant presence service
        try:
            presence_healthy = await context.participant_presence_client.health_check()
            health_status["dependencies"]["participant_presence"] = {
                "status": "healthy" if presence_healthy else "unhealthy",
                "url": context.participant_presence_client.base_url
            }
        except Exception as e:
            health_status["dependencies"]["participant_presence"] = {
                "status": "error",
                "error": str(e),
                "url": context.participant_presence_client.base_url
            }
        
        # Check queue manager
        try:
            queue_status = await context.queue_manager.get_queue_status()
            health_status["dependencies"]["queue_manager"] = {
                "status": "healthy",
                "total_queued": queue_status.total_queued
            }
        except Exception as e:
            health_status["dependencies"]["queue_manager"] = {
                "status": "error",
                "error": str(e)
            }
        
        # Determine overall health
        dependency_statuses = [
            dep["status"] for dep in health_status["dependencies"].values()
        ]
        
        if "error" in dependency_statuses:
            health_status["status"] = "degraded"
        elif "unhealthy" in dependency_statuses:
            health_status["status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        return {
            "service": "queuing-service",
            "status": "error",
            "timestamp": __import__('datetime').datetime.utcnow().isoformat(),
            "error": str(e)
        }
