"""
FastAPI dependency injection functions for the queuing service.

This module provides dependency injection functions that can be used
with FastAPI's Depends() to inject shared resources into endpoints.
"""

from fastapi import Depends

from context import get_app_context, ApplicationContext
from services.queue_manager import QueueManager
from services.routing_engine import RoutingEngine
from services.participant_presence_client import ParticipantPresenceClient


async def get_app_context_dependency() -> ApplicationContext:
    """
    FastAPI dependency to get the application context.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await get_app_context()


async def get_queue_manager(
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> QueueManager:
    """
    FastAPI dependency to get the queue manager.
    
    Args:
        context: The application context (injected by FastAPI)
        
    Returns:
        QueueManager: The queue manager instance
    """
    return context.queue_manager


async def get_routing_engine(
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> RoutingEngine:
    """
    FastAPI dependency to get the routing engine.
    
    Args:
        context: The application context (injected by FastAPI)
        
    Returns:
        RoutingEngine: The routing engine instance
    """
    return context.routing_engine


async def get_participant_presence_client(
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> ParticipantPresenceClient:
    """
    FastAPI dependency to get the participant presence client.
    
    Args:
        context: The application context (injected by FastAPI)
        
    Returns:
        ParticipantPresenceClient: The participant presence client instance
    """
    return context.participant_presence_client
