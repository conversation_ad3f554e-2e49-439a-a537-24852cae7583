"""
Main application module for the queuing service.

This module sets up the FastAPI application, initializes the application context,
and starts the Kafka event consumer for processing call.started events.
"""

import asyncio
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from config import settings
from context import get_app_context
from api.queue_routes import router as queue_router
from services.event_handlers import EventHandlers

# Configure logging
logging.basicConfig(
    level=logging.INFO if not settings.debug else logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan context manager.

    Handles initialization and cleanup of application resources including
    the application context, Kafka consumers, and background tasks.
    """
    # Startup
    logger.info("Starting Queuing Service...")

    try:
        # Initialize application context
        context = await get_app_context()
        await context.initialize()
        logger.info("Application context initialized")

        # Start queue manager
        await context.queue_manager.start()
        logger.info("Queue manager started")

        # Initialize event handlers
        event_handlers = EventHandlers(context)

        # Register Kafka event handlers
        context.event_consumer.register_handler(
            "call-controller.call-started",
            event_handlers.handle_call_started
        )
        logger.info("Event handlers registered")

        # Start Kafka consumer in background
        consumer_task = asyncio.create_task(context.event_consumer.consume())
        logger.info("Kafka consumer started")

        # Store references for cleanup
        app.state.context = context
        app.state.event_handlers = event_handlers
        app.state.consumer_task = consumer_task

        logger.info("Queuing Service startup completed")

    except Exception as e:
        logger.error(f"Failed to start Queuing Service: {e}")
        raise

    yield

    # Shutdown
    logger.info("Shutting down Queuing Service...")

    try:
        # Cancel consumer task
        if hasattr(app.state, 'consumer_task'):
            app.state.consumer_task.cancel()
            try:
                await app.state.consumer_task
            except asyncio.CancelledError:
                pass

        # Cleanup event handlers
        if hasattr(app.state, 'event_handlers'):
            await app.state.event_handlers.cleanup()

        # Cleanup application context
        if hasattr(app.state, 'context'):
            await app.state.context.cleanup()

        logger.info("Queuing Service shutdown completed")

    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# Create FastAPI application
app = FastAPI(
    title="Cortexa Queuing Service",
    description="Automatic Call Distribution (ACD) service for managing call queues and routing",
    version="1.0.0",
    lifespan=lifespan,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(queue_router)

# Root endpoint
@app.get("/", summary="Service information")
async def root():
    """
    Get basic service information.

    Returns:
        Dictionary containing service information
    """
    return {
        "service": "queuing-service",
        "version": "1.0.0",
        "description": "Automatic Call Distribution (ACD) service for managing call queues and routing",
        "status": "running"
    }


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug",
    )