"""
Data models for the queuing service.

This module defines all data models used throughout the queuing service,
including queue entries, routing strategies, and API responses.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from uuid import UUID

from pydantic import BaseModel, Field


class CallPriority(str, Enum):
    """Call priority levels for priority-based routing."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class RoutingStrategy(str, Enum):
    """Available routing strategies for call assignment."""
    FIFO = "fifo"  # First-In-First-Out
    PRIORITY = "priority"  # Priority-based routing
    SKILLS_BASED = "skills_based"  # Skills-based routing


class QueueStatus(str, Enum):
    """Status of calls in the queue."""
    QUEUED = "queued"
    ASSIGNED = "assigned"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


class ParticipantRole(str, Enum):
    """Participant roles for routing."""
    OPERATOR = "operator"
    TRANSLATOR = "translator"


class QueuedCall(BaseModel):
    """Represents a call in the queue."""
    
    call_id: UUID
    priority: CallPriority = CallPriority.NORMAL
    required_role: ParticipantRole = ParticipantRole.OPERATOR
    required_skills: list[str] = Field(default_factory=list)
    language_pair: Optional[tuple[str, str]] = None  # (source_lang, target_lang)
    queued_at: datetime = Field(default_factory=datetime.utcnow)
    status: QueueStatus = QueueStatus.QUEUED
    assigned_participant_id: Optional[str] = None
    assigned_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class CallAssignment(BaseModel):
    """Represents a call assignment to a participant."""
    
    call_id: UUID
    participant_id: str
    assigned_at: datetime = Field(default_factory=datetime.utcnow)
    routing_strategy: RoutingStrategy
    assignment_score: Optional[float] = None  # For skills-based routing
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class QueueStatusResponse(BaseModel):
    """Response model for queue status endpoint."""
    
    total_queued: int
    queued_by_priority: Dict[CallPriority, int]
    queued_by_role: Dict[ParticipantRole, int]
    average_wait_time_seconds: float
    longest_wait_time_seconds: float
    available_operators: int
    available_translators: int
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class CallStartedEvent(BaseModel):
    """Event model for call.started events from Kafka."""
    
    event_id: UUID
    event_type: str = "call.started"
    timestamp: datetime
    source_service: str
    call_id: UUID
    priority: CallPriority = CallPriority.NORMAL
    required_role: ParticipantRole = ParticipantRole.OPERATOR
    required_skills: list[str] = Field(default_factory=list)
    language_pair: Optional[tuple[str, str]] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class CallQueuedEvent(BaseModel):
    """Event model for call.queued events to publish to Kafka."""
    
    event_id: UUID = Field(default_factory=lambda: UUID(hex=__import__('uuid').uuid4().hex))
    event_type: str = "call.queued"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    source_service: str = "queuing-service"
    call_id: UUID
    queue_position: int
    estimated_wait_time_seconds: Optional[int] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }


class CallAssignedEvent(BaseModel):
    """Event model for call.assigned events to publish to Kafka."""
    
    event_id: UUID = Field(default_factory=lambda: UUID(hex=__import__('uuid').uuid4().hex))
    event_type: str = "call.assigned"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    source_service: str = "queuing-service"
    call_id: UUID
    participant_id: str
    routing_strategy: RoutingStrategy
    wait_time_seconds: int
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            UUID: lambda v: str(v)
        }
