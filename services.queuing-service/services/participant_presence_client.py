"""
Client for communicating with the Participant Presence Service.

This module provides a client for fetching available operators and translators
from the participant presence service.
"""

import asyncio
from typing import List, Optional
import httpx

from models import ParticipantRole


class ParticipantPresenceClient:
    """
    Client for the Participant Presence Service.
    
    This client handles communication with the participant presence service
    to fetch available operators and translators.
    """
    
    def __init__(self, base_url: str, timeout: float = 10.0):
        """
        Initialize the participant presence client.
        
        Args:
            base_url: Base URL of the participant presence service
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self._client: Optional[httpx.AsyncClient] = None
    
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create the HTTP client."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
        return self._client
    
    async def close(self) -> None:
        """Close the HTTP client."""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def get_available_participants(
        self, 
        role: Optional[ParticipantRole] = None,
        status: str = "available"
    ) -> List[str]:
        """
        Get available participants from the presence service.
        
        Args:
            role: Filter by participant role (operator/translator)
            status: Filter by participant status (default: "available")
            
        Returns:
            List of participant IDs
            
        Raises:
            httpx.HTTPError: If the request fails
        """
        client = await self._get_client()
        
        params = {"status": status}
        if role:
            params["role"] = role.value
        
        try:
            response = await client.get(
                f"{self.base_url}/internal/participants",
                params=params
            )
            response.raise_for_status()
            return response.json()
        except httpx.HTTPError as e:
            raise httpx.HTTPError(f"Failed to fetch participants: {e}")
    
    async def get_available_operators(self) -> List[str]:
        """
        Get available operators.
        
        Returns:
            List of operator IDs
        """
        return await self.get_available_participants(role=ParticipantRole.OPERATOR)
    
    async def get_available_translators(self) -> List[str]:
        """
        Get available translators.
        
        Returns:
            List of translator IDs
        """
        return await self.get_available_participants(role=ParticipantRole.TRANSLATOR)
    
    async def get_participant_skills(self, participant_id: str) -> List[str]:
        """
        Get skills for a specific participant.
        
        Note: This is a placeholder for future implementation.
        The participant presence service would need to be extended
        to support skills information.
        
        Args:
            participant_id: The participant ID
            
        Returns:
            List of skills (empty for now)
        """
        # TODO: Implement when participant presence service supports skills
        return []
    
    async def get_participant_languages(self, participant_id: str) -> List[str]:
        """
        Get supported languages for a specific participant.
        
        Note: This is a placeholder for future implementation.
        The participant presence service would need to be extended
        to support language information.
        
        Args:
            participant_id: The participant ID
            
        Returns:
            List of supported languages (empty for now)
        """
        # TODO: Implement when participant presence service supports languages
        return []
    
    async def health_check(self) -> bool:
        """
        Perform a health check on the participant presence service.
        
        Returns:
            True if the service is healthy, False otherwise
        """
        try:
            client = await self._get_client()
            response = await client.get(f"{self.base_url}/internal/participants")
            return response.status_code == 200
        except Exception:
            return False
