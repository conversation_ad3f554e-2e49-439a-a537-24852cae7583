"""
Routing engine for implementing various call assignment strategies.

This module implements different routing strategies including FIFO,
priority-based routing, and skills-based routing.
"""

import asyncio
from typing import List, Optional, Dict, Any
import logging
import random

from models import (
    QueuedCall, CallAssignment, RoutingStrategy, 
    ParticipantRole, CallPriority
)
from services.participant_presence_client import ParticipantPresenceClient

logger = logging.getLogger(__name__)


class RoutingEngine:
    """
    Implements various routing strategies for call assignment.
    
    This class handles the logic for assigning calls to available
    participants using different strategies like FIFO, priority-based,
    and skills-based routing.
    """
    
    def __init__(
        self, 
        participant_client: ParticipantPresenceClient,
        default_strategy: str = "fifo"
    ):
        """
        Initialize the routing engine.
        
        Args:
            participant_client: Client for fetching available participants
            default_strategy: Default routing strategy to use
        """
        self.participant_client = participant_client
        self.default_strategy = RoutingStrategy(default_strategy)
        
        # Strategy implementations
        self._strategies = {
            RoutingStrategy.FIFO: self._fifo_routing,
            RoutingStrategy.PRIORITY: self._priority_routing,
            RoutingStrategy.SKILLS_BASED: self._skills_based_routing
        }
    
    async def find_assignment(
        self, 
        call: QueuedCall,
        strategy: Optional[RoutingStrategy] = None
    ) -> Optional[CallAssignment]:
        """
        Find the best participant assignment for a call.
        
        Args:
            call: The call to assign
            strategy: Routing strategy to use (defaults to default_strategy)
            
        Returns:
            CallAssignment if a suitable participant is found, None otherwise
        """
        if strategy is None:
            strategy = self.default_strategy
        
        try:
            routing_func = self._strategies.get(strategy)
            if not routing_func:
                logger.error(f"Unknown routing strategy: {strategy}")
                return None
            
            return await routing_func(call)
            
        except Exception as e:
            logger.error(f"Error in routing strategy {strategy} for call {call.call_id}: {e}")
            return None
    
    async def _fifo_routing(self, call: QueuedCall) -> Optional[CallAssignment]:
        """
        First-In-First-Out routing strategy.
        
        Simply assigns the call to any available participant with the required role.
        
        Args:
            call: The call to assign
            
        Returns:
            CallAssignment if successful, None otherwise
        """
        try:
            # Get available participants for the required role
            if call.required_role == ParticipantRole.OPERATOR:
                participants = await self.participant_client.get_available_operators()
            else:
                participants = await self.participant_client.get_available_translators()

            if not participants:
                logger.debug(f"No available {call.required_role.value}s for call {call.call_id}")
                return None

            # Select the first available participant (FIFO)
            selected_participant = participants[0]

            return CallAssignment(
                call_id=call.call_id,
                participant_id=selected_participant.id,
                routing_strategy=RoutingStrategy.FIFO
            )
            
        except Exception as e:
            logger.error(f"Error in FIFO routing for call {call.call_id}: {e}")
            return None
    
    async def _priority_routing(self, call: QueuedCall) -> Optional[CallAssignment]:
        """
        Priority-based routing strategy.
        
        Considers call priority when assigning participants.
        Higher priority calls get preference for assignment.
        
        Args:
            call: The call to assign
            
        Returns:
            CallAssignment if successful, None otherwise
        """
        try:
            # Get available participants for the required role
            if call.required_role == ParticipantRole.OPERATOR:
                participants = await self.participant_client.get_available_operators()
            else:
                participants = await self.participant_client.get_available_translators()

            if not participants:
                logger.debug(f"No available {call.required_role.value}s for call {call.call_id}")
                return None

            # For priority routing, we could implement more sophisticated logic
            # such as reserving certain participants for high-priority calls
            # For now, we'll use the same assignment as FIFO but with priority awareness

            selected_participant = participants[0]

            return CallAssignment(
                call_id=call.call_id,
                participant_id=selected_participant.id,
                routing_strategy=RoutingStrategy.PRIORITY
            )
            
        except Exception as e:
            logger.error(f"Error in priority routing for call {call.call_id}: {e}")
            return None
    
    async def _skills_based_routing(self, call: QueuedCall) -> Optional[CallAssignment]:
        """
        Skills-based routing strategy.
        
        Matches calls to participants based on required skills and languages.
        
        Args:
            call: The call to assign
            
        Returns:
            CallAssignment if successful, None otherwise
        """
        try:
            # Get available participants for the required role
            if call.required_role == ParticipantRole.OPERATOR:
                participants = await self.participant_client.get_available_operators()
            else:
                participants = await self.participant_client.get_available_translators()

            if not participants:
                logger.debug(f"No available {call.required_role.value}s for call {call.call_id}")
                return None

            # Score participants based on skill matching
            participant_scores = []

            for participant in participants:
                score = await self._calculate_skill_score(call, participant.id)
                if score > 0:  # Only consider participants with some matching skills
                    participant_scores.append((participant.id, score))

            if not participant_scores:
                # Fallback to FIFO if no skill matches
                logger.debug(f"No skill matches for call {call.call_id}, falling back to FIFO")
                return await self._fifo_routing(call)

            # Sort by score (highest first) and select the best match
            participant_scores.sort(key=lambda x: x[1], reverse=True)
            selected_participant_id, assignment_score = participant_scores[0]

            return CallAssignment(
                call_id=call.call_id,
                participant_id=selected_participant_id,
                routing_strategy=RoutingStrategy.SKILLS_BASED,
                assignment_score=assignment_score
            )
            
        except Exception as e:
            logger.error(f"Error in skills-based routing for call {call.call_id}: {e}")
            return None
    
    async def _calculate_skill_score(self, call: QueuedCall, participant_id: str) -> float:
        """
        Calculate a skill matching score for a participant and call.
        
        Args:
            call: The call requiring assignment
            participant_id: The participant to score
            
        Returns:
            Skill matching score (0.0 to 1.0)
        """
        try:
            # Get participant skills and languages
            participant_skills = await self.participant_client.get_participant_skills(participant_id)
            participant_languages = await self.participant_client.get_participant_languages(participant_id)
            
            score = 0.0
            total_weight = 0.0
            
            # Score based on required skills
            if call.required_skills:
                skill_weight = 0.6
                total_weight += skill_weight
                
                matching_skills = set(call.required_skills) & set(participant_skills)
                skill_score = len(matching_skills) / len(call.required_skills)
                score += skill_score * skill_weight
            
            # Score based on language requirements
            if call.language_pair:
                language_weight = 0.4
                total_weight += language_weight
                
                source_lang, target_lang = call.language_pair
                language_score = 0.0
                
                if source_lang in participant_languages:
                    language_score += 0.5
                if target_lang in participant_languages:
                    language_score += 0.5
                
                score += language_score * language_weight
            
            # If no specific requirements, give a base score
            if total_weight == 0:
                return 0.5  # Neutral score for participants with no specific requirements
            
            return score / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating skill score for participant {participant_id}: {e}")
            return 0.0
    
    async def get_available_participants_count(self) -> Dict[ParticipantRole, int]:
        """
        Get the count of available participants by role.

        Returns:
            Dictionary with participant counts by role
        """
        try:
            operators = await self.participant_client.get_available_operators()
            translators = await self.participant_client.get_available_translators()

            return {
                ParticipantRole.OPERATOR: len(operators),
                ParticipantRole.TRANSLATOR: len(translators)
            }

        except Exception as e:
            logger.error(f"Error getting participant counts: {e}")
            return {
                ParticipantRole.OPERATOR: 0,
                ParticipantRole.TRANSLATOR: 0
            }
