#!/usr/bin/env python3
"""
Simple integration test for the Queuing Service with updated Participant Presence Service.

This script tests the integration with the new participant response format.
"""

import asyncio
import httpx
from services.participant_presence_client import ParticipantPresenceClient
from models import ParticipantRole


async def test_participant_presence_integration():
    """Test the integration with the updated Participant Presence Service."""
    
    print("🧪 Testing Queuing Service integration with Participant Presence Service...")
    
    # Initialize client
    client = ParticipantPresenceClient("http://localhost:8001")
    
    try:
        print("\n1. Testing health check...")
        health = await client.health_check()
        print(f"   Health check: {'✅ PASS' if health else '❌ FAIL'}")
        
        if not health:
            print("   ⚠️  Participant Presence Service is not available")
            print("   Make sure the service is running on http://localhost:8001")
            return
        
        print("\n2. Testing get all participants...")
        all_participants = await client.get_available_participants()
        print(f"   Found {len(all_participants)} participants")
        
        for participant in all_participants:
            print(f"   - {participant.id} ({participant.role}, {participant.status})")
        
        print("\n3. Testing get available operators...")
        operators = await client.get_available_operators()
        print(f"   Found {len(operators)} available operators")
        
        for operator in operators:
            print(f"   - {operator.id} ({operator.status})")
        
        print("\n4. Testing get available translators...")
        translators = await client.get_available_translators()
        print(f"   Found {len(translators)} available translators")
        
        for translator in translators:
            print(f"   - {translator.id} ({translator.status})")
        
        print("\n5. Testing ID extraction methods...")
        operator_ids = await client.get_available_operator_ids()
        translator_ids = await client.get_available_translator_ids()
        
        print(f"   Operator IDs: {operator_ids}")
        print(f"   Translator IDs: {translator_ids}")
        
        print("\n✅ All integration tests passed!")
        
    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await client.close()


async def test_sample_response():
    """Test parsing a sample response like the one provided."""
    
    print("\n🧪 Testing sample response parsing...")
    
    sample_response = [
        {
            "id": "b1670e5f-5f40-40c3-ae3a-f9677991ca12",
            "role": "operator", 
            "status": "available"
        }
    ]
    
    try:
        from models import Participant
        
        participants = []
        for participant_data in sample_response:
            participant = Participant(**participant_data)
            participants.append(participant)
            print(f"   ✅ Parsed: {participant.id} ({participant.role}, {participant.status})")
        
        print(f"\n✅ Successfully parsed {len(participants)} participants from sample response")
        
    except Exception as e:
        print(f"\n❌ Sample response parsing failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 Starting Queuing Service Integration Tests")
    
    asyncio.run(test_sample_response())
    asyncio.run(test_participant_presence_integration())
    
    print("\n🏁 Integration tests completed")
