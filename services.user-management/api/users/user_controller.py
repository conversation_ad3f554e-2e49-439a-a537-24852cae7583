from fastapi import HTTPException

from context import ApplicationContext
from .user_models import UserCreate, UserResponse


class UserController:
    """
    Controller class for user management operations.
    
    This class contains the business logic for user operations,
    keeping it separate from the API routing layer.
    """
    
    def __init__(self, context: ApplicationContext):
        """
        Initialize the user controller.
        
        Args:
            context: The application context containing shared resources
        """
        self.context = context
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """
        Create a new user and assign them a role.
        
        Args:
            user_data: The user creation data
            
        Returns:
            UserResponse: The created user information
            
        Raises:
            HTTPException: If user creation fails
        """
        try:
            # 1. Create the user in Supabase Auth
            # We auto-confirm the email as it's created by an admin.
            new_user_response = self.context.supabase.auth.admin.create_user({
                "email": user_data.email,
                "password": user_data.password,
                "email_confirm": True,
            })
            
            new_user = new_user_response.user
            if not new_user:
                raise HTTPException(
                    status_code=400, 
                    detail="User creation failed in Supabase Auth."
                )

            # 2. Get the ID of the requested role from the 'roles' table
            role_query = self.context.supabase.table("roles").select("id").eq("name", user_data.role).execute()
            if not role_query.data:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Role '{user_data.role}' not found."
                )
            role_id = role_query.data[0]['id']

            # 3. Assign the role in the 'user_roles' table
            assignment_data, error = self.context.supabase.table("user_roles").insert({
                "user_id": new_user.id,
                "role_id": role_id
            }).execute()

            if error:
                # If role assignment fails, we should delete the created user to roll back
                self.context.supabase.auth.admin.delete_user(new_user.id)
                raise HTTPException(
                    status_code=500, 
                    detail="Failed to assign role to user."
                )

            return UserResponse(
                id=new_user.id,
                email=new_user.email,
                created_at=new_user.created_at.isoformat()
            )

        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            # Convert other exceptions to HTTP exceptions
            raise HTTPException(status_code=500, detail=str(e))
    
    async def assign_role_to_user(self, user_id: str, role_name: str) -> None:
        """
        Assign an additional role to an existing user.
        
        Args:
            user_id: The ID of the user to assign the role to
            role_name: The name of the role to assign
            
        Raises:
            HTTPException: If role assignment fails
        """
        try:
            # Get the role ID
            role_query = self.context.supabase.table("roles").select("id").eq("name", role_name).execute()
            if not role_query.data:
                raise HTTPException(
                    status_code=404, 
                    detail=f"Role '{role_name}' not found."
                )
            role_id = role_query.data[0]['id']

            # Assign the role (insert into user_roles)
            # The unique constraint on (user_id, role_id) will prevent duplicates
            _, error = self.context.supabase.table("user_roles").insert({
                "user_id": user_id,
                "role_id": role_id
            }).execute()

            if error:
                # Handle potential errors, e.g., user already has the role
                if "duplicate key value" in str(error):
                    raise HTTPException(
                        status_code=409, 
                        detail="User already has this role."
                    )
                raise HTTPException(
                    status_code=500, 
                    detail="Failed to assign role."
                )
                
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            # Convert other exceptions to HTTP exceptions
            raise HTTPException(status_code=500, detail=str(e))
