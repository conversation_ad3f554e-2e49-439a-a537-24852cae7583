from fastapi import <PERSON>Router, Depends, status

from context import ApplicationContext
from dependencies import get_app_context_dependency
from .user_models import UserCreate, UserResponse
from .user_controller import UserController


# Create the router for user-related endpoints
router = APIRouter(
    prefix="/users",
    tags=["users"],
    responses={404: {"description": "Not found"}},
)


async def get_user_controller(
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> UserController:
    """
    Dependency to get the user controller instance.
    
    Args:
        context: The application context (injected by FastAPI)
        
    Returns:
        UserController: The user controller instance
    """
    return UserController(context)


@router.post(
    "",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new user",
    description="Creates a new user and assigns them a role. This is an admin-only operation."
)
async def create_user(
    user_data: UserCreate,
    controller: UserController = Depends(get_user_controller)
) -> UserResponse:
    """
    Create a new user and assign them a role.
    
    Note: In a real application, you would protect this endpoint.
    An API Gateway would first validate the JWT of the calling user,
    and only an 'admin' user should be able to access this endpoint.
    
    Args:
        user_data: The user creation data
        controller: The user controller (injected by FastAPI)
        
    Returns:
        UserResponse: The created user information
    """
    return await controller.create_user(user_data)


@router.put(
    "/{user_id}/roles/{role_name}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Assign role to user",
    description="Assigns an additional role to an existing user. This is an admin-only operation."
)
async def assign_role_to_user(
    user_id: str,
    role_name: str,
    controller: UserController = Depends(get_user_controller)
) -> None:
    """
    Assign an additional role to an existing user.
    
    Note: In a real application, you would protect this endpoint.
    An API Gateway would first validate the JWT of the calling user,
    and only an 'admin' user should be able to access this endpoint.
    
    Args:
        user_id: The ID of the user to assign the role to
        role_name: The name of the role to assign
        controller: The user controller (injected by FastAPI)
    """
    await controller.assign_role_to_user(user_id, role_name)
