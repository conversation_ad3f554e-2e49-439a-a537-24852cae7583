from fastapi import FastAP<PERSON>, HTTPException
from supabase import create_client, Client

from config import settings
from models import UserCreate, UserResponse



app = FastAPI(
    title="Cortexa User Management Service",
    description="API for managing user roles and creation.",
    version="1.0.0"
)



# --- API Endpoints ---

# Note: In a real application, you would protect these endpoints.
# An API Gateway would first validate the JWT of the calling user,
# and only an 'admin' user should be able to access these endpoints.

@app.post("/users", response_model=UserResponse, status_code=201)
def create_new_user(user_data: UserCreate):
    """
    Creates a new user and assigns them a role.
    This is an admin-only operation.
    """
    try:
        # 1. Create the user in Supabase Auth
        # We auto-confirm the email as it's created by an admin.
        new_user_response = supabase.auth.admin.create_user({
            "email": user_data.email,
            "password": user_data.password,
            "email_confirm": True,
        })
        
        new_user = new_user_response.user
        if not new_user:
            raise HTTPException(status_code=400, detail="User creation failed in Supabase Auth.")

        # 2. Get the ID of the requested role from the 'roles' table
        role_query = supabase.table("roles").select("id").eq("name", user_data.role).execute()
        if not role_query.data:
            raise HTTPException(status_code=404, detail=f"Role '{user_data.role}' not found.")
        role_id = role_query.data['id']

        # 3. Assign the role in the 'user_roles' table
        assignment_data, error = supabase.table("user_roles").insert({
            "user_id": new_user.id,
            "role_id": role_id
        }).execute()

        if error:
             # If role assignment fails, we should delete the created user to roll back
            supabase.auth.admin.delete_user(new_user.id)
            raise HTTPException(status_code=500, detail="Failed to assign role to user.")

        return {
            "id": new_user.id,
            "email": new_user.email,
            "created_at": new_user.created_at.isoformat()
        }

    except Exception as e:
        # Basic error handling
        raise HTTPException(status_code=500, detail=str(e))


@app.put("/users/{user_id}/roles/{role_name}", status_code=204)
def assign_role_to_user(user_id: str, role_name: str):
    """
    Assigns an additional role to an existing user.
    This is an admin-only operation.
    """
    try:
        # Get the role ID
        role_query = supabase.table("roles").select("id").eq("name", role_name).execute()
        if not role_query.data:
            raise HTTPException(status_code=404, detail=f"Role '{role_name}' not found.")
        role_id = role_query.data['id']

        # Assign the role (insert into user_roles)
        # The unique constraint on (user_id, role_id) will prevent duplicates
        _, error = supabase.table("user_roles").insert({
            "user_id": user_id,
            "role_id": role_id
        }).execute()

        if error:
            # Handle potential errors, e.g., user already has the role
            if "duplicate key value" in str(error):
                 raise HTTPException(status_code=409, detail="User already has this role.")
            raise HTTPException(status_code=500, detail="Failed to assign role.")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# You would also add endpoints for listing users, removing roles, etc.